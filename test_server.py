from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import asyncio
import json

# Simple test server without complex dependencies
app = FastAPI(
    title="Hadith IQ Test Server",
    description="Simple test API for Hadith IQ Project",
    version="1.0.0",
)

# Add CORS middleware to allow Flutter app to connect
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your Flutter app's URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Store connected WebSocket clients
connected_clients = []

@app.get("/")
async def root():
    return {"message": "Hadith IQ Test Server is running!"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "Server is running properly"}

@app.get("/api/test")
async def test_endpoint():
    return {
        "message": "Test endpoint working",
        "backend": "FastAPI",
        "status": "operational"
    }

# Mock endpoints that the Flutter app might expect
@app.get("/api/hadith/test")
async def hadith_test():
    return {
        "message": "Hadith API endpoint working",
        "data": []
    }

@app.get("/api/project/test")
async def project_test():
    return {
        "message": "Project API endpoint working",
        "projects": []
    }

@app.get("/api/narrator/test")
async def narrator_test():
    return {
        "message": "Narrator API endpoint working",
        "narrators": []
    }

@app.websocket("/ws/status")
async def websocket_status(websocket: WebSocket):
    """WebSocket endpoint for server status updates"""
    print("New WebSocket connection attempt")
    await websocket.accept()
    connected_clients.append(websocket)
    print(f"Client connected. Total clients: {len(connected_clients)}")

    try:
        # Send initial status
        status_message = {"status": "online"}
        await websocket.send_text(json.dumps(status_message))
        print("Sent initial status message")

        # Keep connection alive and send periodic status updates
        while True:
            await asyncio.sleep(5)  # Send status every 5 seconds
            status_message = {"status": "online"}
            await websocket.send_text(json.dumps(status_message))
            print("Sent periodic status update")

    except WebSocketDisconnect:
        print("Client disconnected")
        if websocket in connected_clients:
            connected_clients.remove(websocket)
    except Exception as e:
        print(f"WebSocket error: {e}")
        if websocket in connected_clients:
            connected_clients.remove(websocket)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
